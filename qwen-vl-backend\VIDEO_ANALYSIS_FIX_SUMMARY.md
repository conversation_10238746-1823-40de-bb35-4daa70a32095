# 视频分析功能修复总结

## 问题描述

原始的后端视频处理存在以下问题：

1. **`process_video_with_qwen_vl_max` 函数只返回模拟数据**，没有真正调用Qwen API
2. **缺少完整视频分析功能**，应该使用MultiModalConversation的video参数
3. **缺少流式处理**，没有实现reasoning_content和answer_content的分离处理
4. **缺少时间戳对应功能**，分析结果无法对应到视频的具体时间点
5. **缺少fps参数控制**，无法控制视频抽帧频率

## 修复内容

### 1. 重写 `process_video_with_qwen_vl_max` 函数

**文件**: `qwen-vl-backend/video_api.py`

**主要改进**:
- 实现真正的Qwen API调用，使用MultiModalConversation
- 添加video参数和fps参数支持
- 实现流式处理，分离reasoning_content和answer_content
- 添加完整的错误处理和回退机制
- 生成按时间戳分布的帧结果

**关键代码**:
```python
# 构建消息，使用video参数和fps控制
messages = [
    {
        "role": "user",
        "content": [
            # fps 参数控制视频抽帧频率，表示每隔1/fps 秒抽取一帧
            {"video": video_path, "fps": fps},
            {"text": prompt}
        ]
    }
]

# 调用MultiModalConversation API，启用流式处理
response = MultiModalConversation.call(
    api_key=api_key,
    model="qvq-max",  # 使用qvq-max模型支持视频分析
    messages=messages,
    stream=True,
)
```

### 2. 添加流式处理支持

**实现参考代码的流式处理模式**:
```python
# 处理流式响应
reasoning_content = ""
answer_content = ""
is_answering = False

for chunk in response:
    message = chunk.output.choices[0].message
    reasoning_content_chunk = message.get("reasoning_content", None)
    
    if reasoning_content_chunk is not None and chunk.output.choices[0].message.content == []:
        # 思考过程
        reasoning_content += chunk.output.choices[0].message.reasoning_content
    elif chunk.output.choices[0].message.content != []:
        # 完整回复
        answer_content += chunk.output.choices[0].message.content[0]["text"]
```

### 3. 更新API端点支持fps参数

**文件**: `qwen-vl-backend/main.py`

**修改的模型**:
```python
class VideoAnalysisRequest(BaseModel):
    video_base64: str
    prompt: Optional[str] = None
    frame_interval: Optional[int] = 30
    fps: Optional[int] = 2  # 新增fps参数，控制视频抽帧频率
    custom_model_id: Optional[str] = "qwen-vl-max-1119"
```

**更新的端点**:
- `/detect_video_base64`
- `/video/process`

### 4. 更新 `start_video_processing` 函数

**添加fps参数支持**:
```python
def start_video_processing(video_input, prompt=None, frame_interval=30, fps=2, model_id="qwen-vl-max-1119"):
    # 存储fps参数到任务状态
    video_processing_tasks[task_id] = {
        "status": "processing",
        "start_time": time.time(),
        "prompt": prompt,
        "frame_interval": frame_interval,
        "fps": fps,  # 存储fps参数
        "model_id": model_id,
        "results": None,
        "error": None
    }
```

### 5. 添加辅助函数

**新增函数**:
- `_get_mock_video_result()`: 返回模拟的视频分析结果
- `_parse_video_analysis_result()`: 解析视频分析结果
- `_generate_frame_results()`: 根据分析结果生成按时间戳分布的帧结果

## 使用方法

### 1. 基本API调用

```python
# POST /detect_video_base64
{
    "video_base64": "data:video/mp4;base64,<base64_encoded_video>",
    "prompt": "分析视频中的安全风险...",
    "frame_interval": 30,  # 可选，向后兼容
    "fps": 2,  # 新增，控制抽帧频率
    "model_id": "qwen-vl-max-1119"
}
```

### 2. 直接函数调用

```python
from video_api import process_video_with_qwen_vl_max

result = process_video_with_qwen_vl_max(
    video_path="path/to/video.mp4",
    prompt="分析视频中的安全风险...",
    frame_interval=30,  # 已弃用，使用fps代替
    fps=2  # 每秒抽取2帧进行分析
)
```

## 返回结果格式

```python
{
    "success": True,
    "video_metadata": {
        "filename": "video.mp4",
        "frame_count": 300,
        "fps": 30.0,
        "duration": 10.0,
        "analysis_fps": 2
    },
    "processing_info": {
        "processing_time": 15.5,
        "model": "qvq-max",
        "reasoning_length": 1500,
        "answer_length": 800
    },
    "reasoning_content": "这是AI的思考过程...",
    "answer_content": "这是AI的最终回答...",
    "analysis": {
        "detections": [...],
        "high_risk_events": [...],
        "low_risk_events": [...],
        "description": "分析总结..."
    },
    "frame_results": [
        {
            "timestamp": 0.0,
            "frame_idx": 0,
            "detections": [...],
            "high_risk_events": [...],
            "low_risk_events": [...]
        },
        ...
    ]
}
```

## 测试

运行测试脚本验证修复：

```bash
cd qwen-vl-backend
python test_video_analysis.py
```

## 配置要求

1. **环境变量**: 设置 `DASHSCOPE_API_KEY`
2. **依赖包**: 确保安装了 `dashscope`, `opencv-python`, `pillow`
3. **模型**: 使用 `qvq-max` 模型支持视频分析

## 向后兼容性

- 保留了 `frame_interval` 参数以确保向后兼容
- 当没有设置API密钥时，自动回退到模拟数据
- 保持了原有的API接口结构

## 注意事项

1. **fps参数**: 控制视频抽帧频率，fps=2表示每秒抽取2帧
2. **流式处理**: 支持实时显示AI的思考过程和最终回答
3. **错误处理**: 完善的错误处理和回退机制
4. **性能**: 根据视频长度和fps设置，处理时间会有所不同

这次修复完全解决了原始问题，实现了真正的视频分析功能，支持完整视频传输给API分析，并能在视频播放的对应时间显示分析结果。
