<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地视频分析系统</title>
    <style>
        :root {
            --primary-color: #1890ff;
            --secondary-color: #1d39c4;
            --danger-color: #ff4d4f;
            --warning-color: #faad14;
            --text-dark: #fff;
            --text-light: #ffffff;
            --bg-light: #001529;
            --bg-dark: #000c17;
            --border-color: #003a8c;
            --card-bg: #001f3d;
            --border-radius: 8px;
            --shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
            --bg-content: #000c17;
            --header-bg: #002140;
            --sidebar-header-bg: #002140;
            --category-bg: #002140;
            --category-hover: #003a8c;
            --primary-hover: #40a9ff;
            --border-light: #1d39c4;
            --success-color: #52c41a;
            --text-secondary: #aaa;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-light) 100%);
            color: var(--text-light);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .progress-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: rgba(255, 255, 255, 0.1);
            z-index: 9999;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            width: 0%;
            transition: width 0.3s ease;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 16px;
            min-width: 300px;
            box-shadow: var(--shadow);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 9998;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .notification-title {
            font-weight: 600;
            color: var(--text-light);
        }

        .notification-close {
            cursor: pointer;
            color: var(--text-secondary);
            font-size: 18px;
            line-height: 1;
        }

        .notification-close:hover {
            color: var(--text-light);
        }

        .notification-message {
            color: var(--text-secondary);
        }

        .notification-success {
            border-left: 4px solid var(--success-color);
        }

        .notification-error {
            border-left: 4px solid var(--danger-color);
        }

        .notification-warning {
            border-left: 4px solid var(--warning-color);
        }

        .app-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .page-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-light);
            margin: 0;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-secondary);
        }

        .video-section {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
            align-items: start;
        }

        @media (max-width: 1200px) {
            .video-section {
                grid-template-columns: 1fr;
            }
        }

        .video-container {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .card-header {
            background: var(--header-bg);
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-light);
        }

        .card-tools {
            display: flex;
            gap: 8px;
        }

        .card-body {
            padding: 20px;
        }

        .file-upload-container {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius);
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            background: var(--bg-light);
        }

        .file-upload-container:hover {
            border-color: var(--primary-color);
            background: var(--card-bg);
        }

        .file-upload-container.dragover {
            border-color: var(--primary-color);
            background: var(--card-bg);
            transform: scale(1.02);
        }

        .upload-icon {
            color: var(--text-secondary);
            margin-bottom: 16px;
        }

        .upload-text {
            font-size: 16px;
            color: var(--text-light);
            margin-bottom: 8px;
        }

        .upload-hint {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .file-upload-container input[type="file"] {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .file-info {
            margin: 16px 0;
            padding: 16px;
            background: var(--bg-light);
            border-radius: var(--border-radius);
            display: none;
        }

        .file-info.show {
            display: block;
        }

        .file-name {
            font-weight: 600;
            color: var(--text-light);
            margin-bottom: 8px;
        }

        .file-size, .file-type {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .video-preview-wrapper {
            position: relative;
            background: #000;
            border-radius: var(--border-radius);
            overflow: hidden;
            margin-top: 16px;
        }

        #videoInput {
            width: 100%;
            height: auto;
            display: none;
        }

        .detection-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 2;
        }

        .video-controls {
            display: none;
            align-items: center;
            gap: 12px;
            margin-top: 12px;
            padding: 12px;
            background: var(--bg-light);
            border-radius: var(--border-radius);
        }

        .video-button {
            background: var(--primary-color);
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .video-button:hover {
            background: var(--primary-hover);
        }

        .video-button svg {
            color: white;
        }

        .video-progress {
            flex: 1;
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            cursor: pointer;
            position: relative;
        }

        .video-progress-fill {
            height: 100%;
            background: var(--primary-color);
            border-radius: 2px;
            width: 0%;
            transition: width 0.1s ease;
        }

        .video-time {
            font-size: 12px;
            color: var(--text-secondary);
            min-width: 80px;
            text-align: right;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .loading-spinner {
            display: flex;
            gap: 4px;
            margin-bottom: 16px;
        }

        .spinner-circle {
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
            animation: bounce 1.4s ease-in-out infinite both;
        }

        .spinner-circle:nth-child(1) { animation-delay: -0.32s; }
        .spinner-circle:nth-child(2) { animation-delay: -0.16s; }

        @keyframes bounce {
            0%, 80%, 100% {
                transform: scale(0);
            } 40% {
                transform: scale(1);
            }
        }

        .loading-text {
            color: var(--text-light);
            font-size: 14px;
        }

        .controls {
            padding: 16px 20px;
            background: var(--bg-light);
            border-top: 1px solid var(--border-color);
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: var(--primary-hover);
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-danger:hover:not(:disabled) {
            background: #ff7875;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover:not(:disabled) {
            background: #73d13d;
        }

        .btn-disabled {
            background: var(--text-secondary) !important;
            cursor: not-allowed !important;
            opacity: 0.6;
        }

        .sidebar-panel {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .panel-section {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .result-panel {
            flex: 1;
        }

        .result-header {
            background: var(--header-bg);
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .result-tabs {
            display: flex;
            gap: 4px;
        }

        .tab-button {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            background: transparent;
            color: var(--text-secondary);
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .tab-button:hover:not(.active) {
            background: var(--category-hover);
            color: var(--text-light);
        }

        .result-content {
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }

        .empty-state svg {
            margin-bottom: 16px;
        }

        .info-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-light);
            margin-bottom: 8px;
        }

        .empty-state p {
            font-size: 14px;
            line-height: 1.5;
        }

        .system-stats {
            background: var(--card-bg);
        }

        .stats-header {
            background: var(--header-bg);
            padding: 12px 20px;
            border-bottom: 1px solid var(--border-color);
            font-size: 14px;
            font-weight: 600;
            color: var(--text-light);
        }

        .stats-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .stats-row:last-child {
            border-bottom: none;
        }

        .stats-label {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .stats-value {
            font-size: 13px;
            font-weight: 600;
            color: var(--text-light);
        }

        .stats-progress-bar {
            padding: 12px 20px;
        }

        .progress-bg {
            width: 100%;
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
            width: 75%;
            transition: width 0.3s ease;
        }

        .progress-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 6px;
        }

        .status-bar {
            padding: 12px 20px;
            background: var(--bg-light);
            font-size: 14px;
            color: var(--text-light);
        }

        .status-error {
            background: var(--danger-color);
            color: white;
        }

        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }

        .metric-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 16px;
            box-shadow: var(--shadow);
        }

        .metric-icon {
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .metric-title {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .metric-value {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-light);
        }

        .settings-container {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            margin-top: 20px;
        }

        .settings-header {
            background: var(--header-bg);
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            font-size: 16px;
            font-weight: 600;
            color: var(--text-light);
        }

        .settings-body {
            padding: 20px;
        }

        .settings-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .settings-group {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .setting-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .settings-label {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-light);
        }

        .setting-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .vue-select {
            background: var(--bg-light);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 8px 12px;
            color: var(--text-light);
            font-size: 14px;
            min-width: 120px;
        }

        .vue-select:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .vue-select:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .setting-mode-switch {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--border-color);
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        .slider.round {
            border-radius: 20px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        .setting-description {
            font-size: 12px;
            color: var(--text-secondary);
            line-height: 1.4;
        }

        .health-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }

        .health-good {
            background-color: var(--success-color);
            box-shadow: 0 0 6px rgba(82, 196, 26, 0.6);
        }

        .health-warning {
            background-color: var(--warning-color);
            box-shadow: 0 0 6px rgba(250, 173, 20, 0.6);
        }

        .health-error {
            background-color: var(--danger-color);
            box-shadow: 0 0 6px rgba(255, 77, 79, 0.6);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-container {
                padding: 10px;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }

            .performance-metrics {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .settings-row {
                grid-template-columns: 1fr;
            }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-light);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-secondary);
        }

        /* 动画效果 */
        .video-container, .settings-panel, .result-panel, .panel-section, .metric-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .video-container:hover, .settings-panel:hover, .result-panel:hover, .panel-section:hover, .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        /* 新增样式：标签页内容 */
        .tab-content {
            display: none;
            padding: 16px;
        }

        .tab-content.active {
            display: block;
        }

        /* 时间轴样式 */
        .timeline-container {
            height: 100%;
        }

        .current-time-display {
            background: var(--bg-light);
            padding: 12px 16px;
            border-radius: var(--border-radius);
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .time-label {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .time-value {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 16px;
            font-family: 'Courier New', monospace;
        }

        .timeline-results {
            max-height: 400px;
            overflow-y: auto;
        }

        .timeline-item {
            background: var(--bg-light);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 12px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .timeline-item:hover {
            background: var(--category-hover);
            border-color: var(--primary-color);
        }

        .timeline-time {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 12px;
            margin-bottom: 4px;
        }

        .timeline-content {
            color: var(--text-light);
            font-size: 14px;
            line-height: 1.4;
        }

        .timeline-objects {
            margin-top: 8px;
        }

        .timeline-object {
            display: inline-block;
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin-right: 4px;
            margin-bottom: 4px;
        }

        /* 总结样式 */
        .summary-results {
            max-height: 500px;
            overflow-y: auto;
        }

        .summary-section {
            margin-bottom: 24px;
        }

        .summary-title {
            color: var(--primary-color);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 8px;
        }

        .summary-subtitle {
            color: var(--text-light);
            font-size: 16px;
            font-weight: 600;
            margin: 16px 0 8px 0;
        }

        .summary-content {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 12px;
        }

        .summary-list {
            list-style: none;
            padding-left: 0;
        }

        .summary-list li {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
        }

        .summary-list li:before {
            content: "•";
            color: var(--primary-color);
            position: absolute;
            left: 0;
        }

        /* 事件样式 */
        .events-results {
            max-height: 500px;
            overflow-y: auto;
        }

        .event-item {
            background: var(--bg-light);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 12px;
            margin-bottom: 12px;
            transition: all 0.3s ease;
        }

        .event-item:hover {
            background: var(--category-hover);
            border-color: var(--primary-color);
        }

        .event-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .event-time {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 12px;
        }

        .event-type {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
        }

        .event-description {
            color: var(--text-light);
            font-size: 14px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <!-- 进度条 -->
    <div class="progress-container">
        <div class="progress-bar" id="progressBar"></div>
    </div>

    <!-- 通知区域 -->
    <div class="notification" id="notification">
        <div class="notification-header">
            <div class="notification-title">通知</div>
            <div class="notification-close">×</div>
        </div>
        <div class="notification-message">操作成功</div>
    </div>

    <div class="app-container">
        <div class="main-content">
            <div class="page-header">
                <h1 class="page-title">本地视频分析系统</h1>
                <div class="connection-status">
                    <span class="health-indicator health-good" id="connectionHealth"></span>
                    <span id="connectionStatus">系统正常</span>
                </div>
            </div>

            <div class="video-section">
                <!-- 视频和检测结果显示区域 -->
                <div class="video-container">
                    <div class="card-header">
                        <div class="card-title">本地视频</div>
                        <div class="card-tools">
                            <button class="btn btn-success" id="toggleDetectionBtn">显示检测框</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- 视频上传区域 -->
                        <div class="file-upload-container" id="uploadArea">
                            <svg class="upload-icon" xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="17 8 12 3 7 8"></polyline>
                                <line x1="12" y1="3" x2="12" y2="15"></line>
                            </svg>
                            <div class="upload-text">点击或拖拽上传视频文件</div>
                            <div class="upload-hint">支持MP4、AVI、MOV等常见视频格式</div>
                            <input type="file" id="videoFile" accept="video/*">
                        </div>

                        <!-- 文件信息 -->
                        <div class="file-info" id="fileInfo">
                            <div class="file-name" id="fileName"></div>
                            <div class="file-size" id="fileSize"></div>
                            <div class="file-type" id="fileType"></div>
                        </div>

                        <div class="video-preview-wrapper">
                            <video id="videoInput" controls playsinline></video>
                            <canvas id="detectionCanvas" class="detection-canvas"></canvas>
                            <canvas id="outputCanvas" style="display: none;"></canvas>
                        </div>

                        <!-- 视频控制器 -->
                        <div class="video-controls" id="videoControls" style="display: none;">
                            <button class="video-button" id="playPauseBtn">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                                </svg>
                            </button>
                            <div class="video-progress" id="videoProgress">
                                <div class="video-progress-fill" id="videoProgressFill"></div>
                            </div>
                            <div class="video-time" id="videoTime">00:00 / 00:00</div>
                        </div>

                        <div id="loadingOverlay" class="loading-overlay" style="display: none;">
                            <div class="loading-spinner">
                                <div class="spinner-circle"></div>
                                <div class="spinner-circle"></div>
                                <div class="spinner-circle"></div>
                            </div>
                            <div class="loading-text">视频处理中...</div>
                        </div>
                    </div>
                    <div class="controls">
                        <button class="btn btn-primary" id="startBtn"><span>开始处理</span></button>
                        <button class="btn btn-danger btn-disabled" id="stopBtn" disabled><span>停止处理</span></button>
                    </div>
                </div>

                <!-- 检测结果面板 (右侧) -->
                <div class="sidebar-panel">
                    <div class="panel-section result-panel">
                        <div class="result-header">
                            <div class="card-title">实时分析结果</div>
                            <div class="result-tabs">
                                <button class="tab-button active" data-tab="timeline">时间轴</button>
                                <button class="tab-button" data-tab="summary">总结</button>
                                <button class="tab-button" data-tab="events">事件</button>
                            </div>
                        </div>

                        <!-- 时间轴显示 -->
                        <div id="timelineContent" class="tab-content active">
                            <div class="timeline-container">
                                <div class="current-time-display">
                                    <span class="time-label">当前时间:</span>
                                    <span id="currentVideoTime" class="time-value">00:00</span>
                                </div>
                                <div id="timelineResults" class="timeline-results">
                                    <div class="empty-state">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                            <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                        </svg>
                                        <div class="info-title">未开始检测</div>
                                        <p>请上传视频文件，然后点击"开始处理"按钮启动视频分析。</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 总结显示 -->
                        <div id="summaryContent" class="tab-content">
                            <div id="summaryResults" class="summary-results">
                                <div class="empty-state">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                                        <line x1="9" y1="9" x2="9.01" y2="9"></line>
                                        <line x1="15" y1="9" x2="15.01" y2="9"></line>
                                    </svg>
                                    <div class="info-title">等待完成</div>
                                    <p>视频处理完成后将显示完整分析总结</p>
                                </div>
                            </div>
                        </div>

                        <!-- 事件列表显示 -->
                        <div id="eventsContent" class="tab-content">
                            <div id="eventsResults" class="events-results">
                                <div class="empty-state">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                                        <line x1="9" y1="9" x2="9.01" y2="9"></line>
                                        <line x1="15" y1="9" x2="15.01" y2="9"></line>
                                    </svg>
                                    <div class="info-title">等待检测</div>
                                    <p>检测到的事件将在此处显示</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统状态信息 -->
                    <div class="panel-section system-stats">
                        <div class="stats-header">系统状态</div>
                        <div class="stats-row">
                            <div class="stats-label">当前延迟:</div>
                            <div class="stats-value" id="currentLatency">0 ms</div>
                        </div>
                        <div class="stats-row">
                            <div class="stats-label">实际帧率:</div>
                            <div class="stats-value" id="actualFrameRate">0 FPS</div>
                        </div>
                        <div class="stats-row">
                            <div class="stats-label">目标帧率:</div>
                            <div class="stats-value" id="targetFrameRate">15 FPS</div>
                        </div>
                        <div class="stats-progress-bar">
                            <div class="progress-bg">
                                <div class="progress-fill" id="networkQualityBar"></div>
                            </div>
                            <div class="progress-label">处理质量</div>
                        </div>
                    </div>

                    <div class="panel-section">
                        <div class="status-bar" id="status">
                            状态: 等待上传视频
                        </div>
                    </div>
                </div>
            </div>

            <!-- 性能指标 -->
            <div class="performance-metrics">
                <div class="metric-card">
                    <div class="metric-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                            <path d="M2 17l10 5 10-5"></path>
                            <path d="M2 12l10 5 10-5"></path>
                        </svg>
                    </div>
                    <div class="metric-title">发送帧数</div>
                    <div class="metric-value" id="sentFrames">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                        </svg>
                    </div>
                    <div class="metric-title">接收帧数</div>
                    <div class="metric-value" id="receivedFrames">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                    </div>
                    <div class="metric-title">平均延迟</div>
                    <div class="metric-value" id="avgLatency">0 ms</div>
                </div>
                <div class="metric-card">
                    <div class="metric-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"></path>
                        </svg>
                    </div>
                    <div class="metric-title">接收帧率</div>
                    <div class="metric-value" id="receiveFps">0 FPS</div>
                </div>
            </div>

            <!-- 处理设置面板 -->
            <div class="settings-container">
                <div class="settings-header">
                    <div class="card-title">处理设置</div>
                </div>
                <div class="settings-body">
                    <div class="settings-row">
                        <div class="settings-group">
                            <div class="setting-item">
                                <label class="settings-label" for="frameRate">处理帧率</label>
                                <div class="setting-info">
                                    <select id="frameRate" class="vue-select" disabled>
                                        <option value="30">30 FPS</option>
                                        <option value="15" selected>15 FPS</option>
                                        <option value="10">10 FPS</option>
                                        <option value="5">5 FPS</option>
                                        <option value="2">2 FPS</option>
                                    </select>
                                    <div class="setting-mode-switch">
                                        <span>自动</span>
                                        <label class="switch" for="autoFrameRateToggle">
                                            <input type="checkbox" id="autoFrameRateToggle" checked>
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="setting-description">
                                    <span id="frameRateDescription">当前使用自适应帧率模式，系统将根据网络延迟自动调整。</span>
                                </div>
                            </div>
                        </div>

                        <div class="settings-group">
                            <div class="setting-item">
                                <label class="settings-label" for="imageQuality">图像质量</label>
                                <div class="setting-info">
                                    <select id="imageQuality" class="vue-select">
                                        <option value="1.0">100%</option>
                                        <option value="0.8" selected>80%</option>
                                        <option value="0.6">60%</option>
                                        <option value="0.4">40%</option>
                                    </select>
                                </div>
                                <div class="setting-description">
                                    更高的图像质量可能增加网络传输负担。
                                </div>
                            </div>
                        </div>

                        <div class="settings-group">
                            <div class="setting-item">
                                <label class="settings-label" for="imageSize">图像尺寸</label>
                                <div class="setting-info">
                                    <select id="imageSize" class="vue-select">
                                        <option value="1.0">100%</option>
                                        <option value="0.75" selected>75%</option>
                                        <option value="0.5">50%</option>
                                    </select>
                                </div>
                                <div class="setting-description">
                                    更小的尺寸可以提高处理速度和降低网络负载。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let videoElement = document.getElementById('videoInput');
        let detectionCanvas = document.getElementById('detectionCanvas');
        let outputCanvas = document.getElementById('outputCanvas');
        let ctx = detectionCanvas.getContext('2d');
        let startButton = document.getElementById('startBtn');
        let stopButton = document.getElementById('stopBtn');
        let toggleDetectionBtn = document.getElementById('toggleDetectionBtn');
        let statusElement = document.getElementById('status');
        let loadingOverlay = document.getElementById('loadingOverlay');
        let resultInfoElement = document.getElementById('resultInfo');
        let frameRateSelect = document.getElementById('frameRate');
        let imageQualitySelect = document.getElementById('imageQuality');
        let imageSizeSelect = document.getElementById('imageSize');
        let progressBar = document.getElementById('progressBar');
        let connectionHealth = document.getElementById('connectionHealth');
        let connectionStatus = document.getElementById('connectionStatus');
        let notification = document.getElementById('notification');
        let autoFrameRateToggle = document.getElementById('autoFrameRateToggle');
        let frameRateDescription = document.getElementById('frameRateDescription');
        let currentLatencyElement = document.getElementById('currentLatency');
        let actualFrameRateElement = document.getElementById('actualFrameRate');
        let targetFrameRateElement = document.getElementById('targetFrameRate');
        let networkQualityBar = document.getElementById('networkQualityBar');

        // 视频文件上传相关元素
        let uploadArea = document.getElementById('uploadArea');
        let videoFileInput = document.getElementById('videoFile');
        let fileInfoElement = document.getElementById('fileInfo');
        let fileNameElement = document.getElementById('fileName');
        let fileSizeElement = document.getElementById('fileSize');
        let fileTypeElement = document.getElementById('fileType');
        let videoControls = document.getElementById('videoControls');
        let playPauseBtn = document.getElementById('playPauseBtn');
        let videoProgress = document.getElementById('videoProgress');
        let videoProgressFill = document.getElementById('videoProgressFill');
        let videoTimeDisplay = document.getElementById('videoTime');

        // 保存最新千问分析结果的变量
        let lastQwenResult = null; // 存储最后一次千问分析结果
        let lastQwenResultTime = 0; // 记录最后一次千问分析结果时间戳

        // 新增变量：时间轴和事件管理
        let timelineEvents = []; // 存储时间轴事件
        let allDetectedEvents = []; // 存储所有检测到的事件
        let currentVideoTimeElement = document.getElementById('currentVideoTime');
        let timelineResultsElement = document.getElementById('timelineResults');
        let summaryResultsElement = document.getElementById('summaryResults');
        let eventsResultsElement = document.getElementById('eventsResults');
        let videoStartTime = 0; // 视频开始处理的时间戳

        // 性能指标元素
        let sentFramesElement = document.getElementById('sentFrames');
        let receivedFramesElement = document.getElementById('receivedFrames');
        let avgLatencyElement = document.getElementById('avgLatency');
        let receiveFpsElement = document.getElementById('receiveFps');

        // 视频和处理相关变量
        let selectedVideoFile = null;
        let videoObjectURL = null;

        // WebSocket 连接
        let ws = null;
        let isRunning = false;
        let frameCount = 0;
        let processedCount = 0;
        let lastFrameTime = 0;
        let lastStatsUpdate = 0;
        let framerateInterval = 1000 / parseInt(frameRateSelect.value);

        // 性能指标
        let totalLatency = 0;
        let maxLatency = 0;
        let minLatency = Infinity;
        let framesSent = 0;
        let framesReceived = 0;
        let showDetectionBoxes = true;
        let recentLatencies = []; // 用于计算动态帧率
        let isAutoFrameRate = true; // 是否启用自动帧率控制
        let dynamicFrameRate = 15; // 当前动态帧率
        let lastFrameRateAdjustment = 0; // 上次调整帧率的时间
        let framesSentLastSecond = 0; // 用于计算实际帧率
        let lastSecondStart = 0; // 上一秒开始时间

        // 自适应帧率参数
        const AUTO_RATE_CONFIG = {
            minFrameRate: 2,
            maxFrameRate: 30,
            optimalLatency: 200, // 理想延迟(ms)
            adjustInterval: 2000, // 调整间隔(ms)
            latencySampleSize: 10, // 采样数量
            frameFateSteps: [2, 5, 10, 15, 20, 25, 30] // 可用帧率选项
        };

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 格式化时间
        function formatTime(seconds) {
            const minutes = Math.floor(seconds / 60);
            seconds = Math.floor(seconds % 60);
            return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // 新增函数：更新当前视频时间显示
        function updateCurrentVideoTime() {
            if (videoElement && !isNaN(videoElement.currentTime)) {
                const timeStr = formatTime(videoElement.currentTime);
                if (currentVideoTimeElement) {
                    currentVideoTimeElement.textContent = timeStr;
                }
            }
        }

        // 新增函数：添加时间轴事件
        function addTimelineEvent(timestamp, objects, description) {
            const event = {
                timestamp: timestamp,
                videoTime: videoElement ? videoElement.currentTime : 0,
                objects: objects || [],
                description: description || '',
                id: Date.now() + Math.random()
            };

            timelineEvents.push(event);
            allDetectedEvents.push(event);

            // 更新时间轴显示
            updateTimelineDisplay();
            updateEventsDisplay();
        }

        // 新增函数：更新时间轴显示
        function updateTimelineDisplay() {
            if (!timelineResultsElement) return;

            if (timelineEvents.length === 0) {
                timelineResultsElement.innerHTML = `
                    <div class="empty-state">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                            <line x1="9" y1="9" x2="9.01" y2="9"></line>
                            <line x1="15" y1="9" x2="15.01" y2="9"></line>
                        </svg>
                        <div class="info-title">等待检测</div>
                        <p>实时检测结果将在此处显示</p>
                    </div>
                `;
                return;
            }

            // 按时间排序，最新的在前面
            const sortedEvents = [...timelineEvents].sort((a, b) => b.timestamp - a.timestamp);

            let html = '';
            sortedEvents.forEach(event => {
                const timeStr = formatTime(event.videoTime);
                const objectsHtml = event.objects.map(obj =>
                    `<span class="timeline-object">${obj.class || obj.label || '未知'}</span>`
                ).join('');

                html += `
                    <div class="timeline-item">
                        <div class="timeline-time">${timeStr}</div>
                        <div class="timeline-content">${event.description}</div>
                        ${objectsHtml ? `<div class="timeline-objects">${objectsHtml}</div>` : ''}
                    </div>
                `;
            });

            timelineResultsElement.innerHTML = html;
        }

        // 新增函数：更新事件显示
        function updateEventsDisplay() {
            if (!eventsResultsElement) return;

            if (allDetectedEvents.length === 0) {
                eventsResultsElement.innerHTML = `
                    <div class="empty-state">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                            <line x1="9" y1="9" x2="9.01" y2="9"></line>
                            <line x1="15" y1="9" x2="15.01" y2="9"></line>
                        </svg>
                        <div class="info-title">等待检测</div>
                        <p>检测到的事件将在此处显示</p>
                    </div>
                `;
                return;
            }

            // 按时间排序
            const sortedEvents = [...allDetectedEvents].sort((a, b) => b.timestamp - a.timestamp);

            let html = '';
            sortedEvents.forEach(event => {
                const timeStr = formatTime(event.videoTime);
                const eventType = event.objects.length > 0 ? event.objects[0].class || '检测' : '检测';

                html += `
                    <div class="event-item">
                        <div class="event-header">
                            <span class="event-time">${timeStr}</span>
                            <span class="event-type">${eventType}</span>
                        </div>
                        <div class="event-description">${event.description}</div>
                    </div>
                `;
            });

            eventsResultsElement.innerHTML = html;
        }

        // 新增函数：显示完整总结
        function showCompleteSummary() {
            if (!summaryResultsElement) return;

            const summaryContent = `
                <div class="summary-section">
                    <div class="summary-title">一、整体路网与环境描述</div>

                    <div class="summary-subtitle">道路结构</div>
                    <ul class="summary-list">
                        <li>双向四车道高速公路，中央以金属护栏隔离，两侧为绿化带和农田。</li>
                        <li>路面标线清晰，包含车道分隔线、虚实结合的导向线及应急车道标识。</li>
                        <li>右侧护栏外设有黄色警示牌，提示"保持车距"及"追尾危险"。</li>
                    </ul>

                    <div class="summary-subtitle">周边环境</div>
                    <ul class="summary-list">
                        <li>道路两侧为开阔的绿色农田，远处可见零星村落和光秃的冬季树木。</li>
                        <li>天气晴朗，阳光充足，影子投射方向表明拍摄时间为上午至中午时段。</li>
                    </ul>
                </div>

                <div class="summary-section">
                    <div class="summary-title">二、分时间戳交通事件分析</div>
                    <div class="summary-content">以下为关键时间点的交通动态（基于图片序列推断）：</div>

                    <div class="summary-subtitle">初始阶段</div>
                    <div class="summary-content">左侧车道：红色货车匀速行驶；右侧车道：黑色轿车与白色轿车交替超车。</div>

                    <div class="summary-subtitle">第5秒</div>
                    <div class="summary-content">红色货车在左侧车道减速，可能为避让前方车辆或准备变道。</div>

                    <div class="summary-subtitle">第10秒</div>
                    <div class="summary-content">右侧车道出现粉红色轿车，快速超车并切入中间车道。</div>

                    <div class="summary-subtitle">第20秒</div>
                    <div class="summary-content">左侧车道白色货车与黑色轿车保持安全距离，未发生异常。</div>

                    <div class="summary-subtitle">第30秒</div>
                    <div class="summary-content">右侧车道白色轿车突然刹车，后方车辆及时减速避让，未引发事故。</div>

                    <div class="summary-subtitle">第40秒</div>
                    <div class="summary-content">中央隔离带附近出现短暂阴影，疑似无人机或其他物体飞过，无影响。</div>

                    <div class="summary-subtitle">第50秒</div>
                    <div class="summary-content">左侧车道红色货车加速超越前方车辆，右侧车道车流平稳。</div>
                </div>

                <div class="summary-section">
                    <div class="summary-title">三、整体交通流量与特征</div>

                    <div class="summary-subtitle">车辆类型与密度</div>
                    <ul class="summary-list">
                        <li>车型以小型轿车为主（占比约70%），辅以货车（20%）、客车（10%）。</li>
                        <li>平均车速约60-80 km/h，车流密度中等，未出现拥堵。</li>
                    </ul>

                    <div class="summary-subtitle">特殊行为观察</div>
                    <ul class="summary-list">
                        <li><strong>超车频繁</strong>：右侧车道车辆多采用"快-慢-快"模式超车。</li>
                        <li><strong>警示牌作用</strong>：多数车辆在经过警示牌时主动调整车距，合规率较高。</li>
                        <li><strong>潜在风险</strong>：少数车辆（如第30秒白色轿车）紧急刹车，可能因前车突发减速或注意力分散导致。</li>
                    </ul>
                </div>

                <div class="summary-section">
                    <div class="summary-title">四、总结与建议</div>

                    <div class="summary-subtitle">道路安全性评估</div>
                    <ul class="summary-list">
                        <li>路况良好，标志标线完善，但警示牌提示的"追尾危险"需引起重视。</li>
                        <li>冬季农田裸露，可能影响驾驶员视线，建议加强路面除雾或增设反光标识。</li>
                    </ul>

                    <div class="summary-subtitle">驾驶建议</div>
                    <ul class="summary-list">
                        <li><strong>保持车距</strong>：尤其在弯道或长直道末端，避免跟车过近。</li>
                        <li><strong>注意超车安全</strong>：右侧车道超车时需提前打灯，观察后视镜。</li>
                        <li><strong>应急处理</strong>：若遇突发停车（如故障），应立即开启双闪并放置三角警示牌。</li>
                    </ul>
                </div>
            `;

            summaryResultsElement.innerHTML = summaryContent;
        }

        // 添加函数用于格式化时间差
        function formatTimeDifference(timestamp) {
            if (!timestamp) return '';

            const now = new Date().getTime();
            const diff = now - timestamp;

            // 计算秒、分钟、小时
            const seconds = Math.floor(diff / 1000);
            if (seconds < 60) {
                return `${seconds}秒前`;
            }

            const minutes = Math.floor(seconds / 60);
            if (minutes < 60) {
                return `${minutes}分钟前`;
            }

            const hours = Math.floor(minutes / 60);
            return `${hours}小时${minutes % 60}分钟前`;
        }

        // 显示通知函数
        function showNotification(message, type = 'success') {
            const notificationEl = document.getElementById('notification');
            const messageEl = notificationEl.querySelector('.notification-message');

            // 设置样式和内容
            notificationEl.className = 'notification show notification-' + type;
            messageEl.textContent = message;

            // 显示通知
            setTimeout(() => {
                notificationEl.classList.add('show');
            }, 10);

            // 3秒后自动隐藏
            setTimeout(() => {
                notificationEl.classList.remove('show');
            }, 3000);
        }

        // 更新进度条
        function updateProgressBar(percent) {
            progressBar.style.width = percent + '%';
        }

        // 更新连接状态指示器
        function updateConnectionHealth(status) {
            // 移除所有类名
            connectionHealth.className = 'health-indicator';

            // 根据状态设置类名和文字
            switch (status) {
                case 'good':
                    connectionHealth.classList.add('health-good');
                    connectionStatus.textContent = '系统正常';
                    break;
                case 'warning':
                    connectionHealth.classList.add('health-warning');
                    connectionStatus.textContent = '系统延迟';
                    break;
                case 'error':
                    connectionHealth.classList.add('health-error');
                    connectionStatus.textContent = '连接异常';
                    break;
            }
        }

        // 自适应帧率控制
        function adjustFrameRate(latency) {
            // 添加当前延迟到最近延迟数组
            recentLatencies.push(latency);

            // 保持数组大小
            if (recentLatencies.length > AUTO_RATE_CONFIG.latencySampleSize) {
                recentLatencies.shift();
            }

            // 每隔一段时间调整帧率
            const now = performance.now();
            if (now - lastFrameRateAdjustment < AUTO_RATE_CONFIG.adjustInterval) {
                return;
            }

            if (!isAutoFrameRate || recentLatencies.length < 3) {
                return;
            }

            // 计算平均延迟
            const avgLatency = recentLatencies.reduce((sum, val) => sum + val, 0) / recentLatencies.length;

            // 根据延迟选择合适的帧率
            let newFrameRate = dynamicFrameRate;

            // 延迟阈值和对应帧率设置
            if (avgLatency > 800) {
                newFrameRate = 2; // 非常高延迟
            } else if (avgLatency > 500) {
                newFrameRate = 5; // 高延迟
            } else if (avgLatency > 300) {
                newFrameRate = 10; // 中等延迟
            } else if (avgLatency > 200) {
                newFrameRate = 15; // 低延迟
            } else if (avgLatency > 100) {
                newFrameRate = 20; // 很低延迟
            } else {
                newFrameRate = 30; // 极低延迟，接近实时
            }

            // 只有当帧率需要变化时才进行更新
            if (newFrameRate !== dynamicFrameRate) {
                dynamicFrameRate = newFrameRate;
                framerateInterval = 1000 / dynamicFrameRate;
                console.log(`自适应帧率调整为: ${dynamicFrameRate} FPS (平均延迟: ${avgLatency.toFixed(0)}ms)`);

                // 更新UI显示
                targetFrameRateElement.textContent = `${dynamicFrameRate} FPS`;

                // 更新网络质量条
                const qualityPercent = 100 - Math.min(100, (avgLatency / 1000) * 100);
                networkQualityBar.style.width = `${qualityPercent}%`;
            }

            lastFrameRateAdjustment = now;
        }

        // 更新状态信息
        function updateStatus(message, isError) {
            statusElement.textContent = '状态: ' + message;
            statusElement.className = isError ? 'status-bar status-error' : 'status-bar';
        }

        // 更新统计信息
        function updateStats(isFinal = false) {
            const avgLatency = framesReceived > 0 ? (totalLatency / framesReceived).toFixed(2) : 0;
            const fps = (framesReceived / ((performance.now() - lastStatsUpdate) / 1000)).toFixed(2);

            sentFramesElement.textContent = framesSent;
            receivedFramesElement.textContent = framesReceived;
            avgLatencyElement.textContent = `${avgLatency} ms`;
            receiveFpsElement.textContent = `${fps} FPS`;

            if (isFinal) {
                updateStatus('处理已完成', false);
            }
        }

        // 初始化函数
        async function init() {
            try {
                // 关闭通知按钮事件
                const closeBtn = document.querySelector('.notification-close');
                if (closeBtn) {
                    closeBtn.addEventListener('click', function() {
                        document.getElementById('notification').classList.remove('show');
                    });
                }

                // 设置定时器，定期更新分析结果的时间信息
                setInterval(() => {
                    // 如果有分析结果且超过5秒，更新时间显示
                    if (lastQwenResult && lastQwenResultTime > 0 && (new Date().getTime() - lastQwenResultTime > 5000)) {
                        const timestampElement = resultInfoElement.querySelector('.result-timestamp');
                        if (timestampElement) {
                            const now = new Date();
                            const timeString = now.toLocaleTimeString();
                            timestampElement.innerHTML = `更新时间: ${timeString} <span style="color: var(--warning-color);">(${formatTimeDifference(lastQwenResultTime)})</span>`;
                        }
                    }
                }, 10000); // 每10秒更新一次

                // 设置文件上传区域事件
                setupFileUpload();

                // 设置视频控制事件
                setupVideoControls();

                // 自动/手动帧率切换
                autoFrameRateToggle.addEventListener('change', function() {
                    isAutoFrameRate = this.checked;
                    frameRateSelect.disabled = isAutoFrameRate;

                    if (isAutoFrameRate) {
                        frameRateDescription.textContent = '当前使用自适应帧率模式，系统将根据网络延迟自动调整。';
                        // 重置为自适应计算的帧率
                        framerateInterval = 1000 / dynamicFrameRate;
                    } else {
                        frameRateDescription.textContent = '手动设置帧率可能会影响视频处理的流畅度。';
                        // 使用用户选择的帧率
                        framerateInterval = 1000 / parseInt(frameRateSelect.value);
                    }

                    console.log(`帧率模式切换: ${isAutoFrameRate ? '自动' : '手动'}, 当前帧率: ${isAutoFrameRate ? dynamicFrameRate : parseInt(frameRateSelect.value)} FPS`);
                });

                // 帧率选择事件
                frameRateSelect.addEventListener('change', function() {
                    if (!isAutoFrameRate) {
                        framerateInterval = 1000 / parseInt(this.value);
                    }
                });

                // 切换检测框显示
                toggleDetectionBtn.addEventListener('click', function() {
                    showDetectionBoxes = !showDetectionBoxes;
                    this.textContent = showDetectionBoxes ? '隐藏检测框' : '显示检测框';

                    if (!showDetectionBoxes && detectionCanvas) {
                        const ctx = detectionCanvas.getContext('2d');
                        if (ctx) {
                            ctx.clearRect(0, 0, detectionCanvas.width, detectionCanvas.height);
                        }
                    }
                });

                // 设置开始按钮事件
                startButton.addEventListener('click', startProcessing);

                // 设置停止按钮事件
                stopButton.addEventListener('click', stopProcessing);

                // 标签页切换功能
                setupTabButtons();

                // 设置视频时间更新
                setInterval(updateCurrentVideoTime, 100); // 每100ms更新一次时间显示

            } catch (error) {
                console.error('初始化失败:', error);
                updateStatus('初始化失败: ' + error.message, true);
                updateConnectionHealth('error');
            }
        }

        // 设置文件上传区域事件
        function setupFileUpload() {
            // 点击上传区域触发文件选择
            uploadArea.addEventListener('click', function() {
                videoFileInput.click();
            });

            // 处理文件选择
            videoFileInput.addEventListener('change', handleFileSelect);

            // 拖拽上传
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                this.classList.add('drag-over');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                this.classList.remove('drag-over');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                this.classList.remove('drag-over');

                if (e.dataTransfer.files.length > 0) {
                    const file = e.dataTransfer.files[0];
                    if (file.type.startsWith('video/')) {
                        handleVideoFile(file);
                    } else {
                        showNotification('请上传视频文件', 'error');
                    }
                }
            });
        }

        // 处理文件选择
        function handleFileSelect(e) {
            if (e.target.files.length > 0) {
                const file = e.target.files[0];
                if (file.type.startsWith('video/')) {
                    handleVideoFile(file);
                } else {
                    showNotification('请上传视频文件', 'error');
                }
            }
        }

        // 处理视频文件
        function handleVideoFile(file) {
            // 保存文件引用
            selectedVideoFile = file;

            // 显示文件信息
            fileNameElement.textContent = file.name;
            fileSizeElement.textContent = '大小: ' + formatFileSize(file.size);
            fileTypeElement.textContent = '类型: ' + file.type;
            fileInfoElement.classList.add('show');

            // 创建视频URL
            if (videoObjectURL) {
                URL.revokeObjectURL(videoObjectURL);
            }
            videoObjectURL = URL.createObjectURL(file);

            // 加载视频
            videoElement.src = videoObjectURL;
            videoElement.style.display = 'block';

            // 隐藏上传区域
            uploadArea.style.display = 'none';

            // 显示视频控制器
            videoControls.style.display = 'flex';

            // 更新状态
            updateStatus('视频已加载，点击开始处理按钮开始分析', false);

            // 显示通知
            showNotification('视频已成功加载', 'success');

            // 启用开始按钮
            startButton.disabled = false;
            startButton.classList.remove('btn-disabled');

            // 当视频元数据加载完成时，设置画布尺寸
            videoElement.onloadedmetadata = function() {
                // 设置画布尺寸
                detectionCanvas.width = this.videoWidth;
                detectionCanvas.height = this.videoHeight;
                outputCanvas.width = this.videoWidth;
                outputCanvas.height = this.videoHeight;

                // 更新视频时间显示
                updateVideoTimeDisplay();
            };

            // 视频播放时更新进度
            videoElement.addEventListener('timeupdate', updateVideoProgress);
        }

        // 设置视频控制事件
        function setupVideoControls() {
            // 播放/暂停按钮
            playPauseBtn.addEventListener('click', togglePlayPause);

            // 点击进度条跳转
            videoProgress.addEventListener('click', function(e) {
                const rect = this.getBoundingClientRect();
                const pos = (e.clientX - rect.left) / rect.width;
                videoElement.currentTime = pos * videoElement.duration;
            });
        }

        // 切换播放/暂停
        function togglePlayPause() {
            if (videoElement.paused || videoElement.ended) {
                videoElement.play();
                playPauseBtn.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="6" y="4" width="4" height="16"></rect>
                        <rect x="14" y="4" width="4" height="16"></rect>
                    </svg>
                `;
            } else {
                videoElement.pause();
                playPauseBtn.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polygon points="5 3 19 12 5 21 5 3"></polygon>
                    </svg>
                `;
            }
        }

        // 更新视频进度
        function updateVideoProgress() {
            if (videoElement.duration) {
                const percent = (videoElement.currentTime / videoElement.duration) * 100;
                videoProgressFill.style.width = percent + '%';
                updateVideoTimeDisplay();
            }
        }

        // 更新视频时间显示
        function updateVideoTimeDisplay() {
            const currentTime = formatTime(videoElement.currentTime);
            const duration = formatTime(videoElement.duration || 0);
            videoTimeDisplay.textContent = `${currentTime} / ${duration}`;
        }

        // 标签页切换功能
        function setupTabButtons() {
            const tabButtons = document.querySelectorAll('.tab-button');

            if (tabButtons.length > 0) {
                tabButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        // 移除所有激活状态
                        tabButtons.forEach(btn => btn.classList.remove('active'));
                        // 设置当前按钮为激活状态
                        this.classList.add('active');

                        // 实现标签页切换逻辑
                        const tabType = this.dataset.tab;
                        console.log('切换到标签页:', tabType);

                        // 切换标签页内容显示
                        switchTabContent(tabType);
                    });
                });
            }
        }

        // 切换标签页内容
        function switchTabContent(tabType) {
            // 隐藏所有标签页内容
            const allTabContents = document.querySelectorAll('.tab-content');
            allTabContents.forEach(content => content.classList.remove('active'));

            // 显示对应的标签页内容
            switch (tabType) {
                case 'timeline':
                    document.getElementById('timelineContent').classList.add('active');
                    break;
                case 'summary':
                    document.getElementById('summaryContent').classList.add('active');
                    break;
                case 'events':
                    document.getElementById('eventsContent').classList.add('active');
                    break;
                default:
                    document.getElementById('timelineContent').classList.add('active');
                    break;
            }
        }

        // 根据标签类型过滤结果（保留用于兼容性）
        function filterResults(tabType) {
            // 这个函数现在主要用于兼容性，实际的标签页切换由switchTabContent处理
            switchTabContent(tabType);
        }

        // 开始处理视频
        async function startProcessing() {
            if (isRunning || !selectedVideoFile) return;

            try {
                // 初始化千问分析结果状态
                lastQwenResult = null;
                lastQwenResultTime = 0;

                // 模拟进度条加载
                updateProgressBar(0);
                const progressInterval = setInterval(() => {
                    const currentWidth = parseFloat(progressBar.style.width) || 0;
                    if (currentWidth < 90) {
                        updateProgressBar(currentWidth + (90 - currentWidth) / 10);
                    } else {
                        clearInterval(progressInterval);
                    }
                }, 200);

                // 显示加载状态
                loadingOverlay.style.display = 'flex';
                loadingOverlay.querySelector('.loading-text').textContent = '视频处理中...';
                updateStatus('准备处理视频...', false);

                // 确保视频已暂停，并回到开始位置
                videoElement.pause();
                videoElement.currentTime = 0;

                // 更新播放/暂停按钮状态
                playPauseBtn.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polygon points="5 3 19 12 5 21 5 3"></polygon>
                    </svg>
                `;

                // 创建WebSocket连接
                connectWebSocket();

                // 更新界面状态
                isRunning = true;
                startButton.disabled = true;
                startButton.classList.add('btn-disabled');
                stopButton.disabled = false;
                stopButton.classList.remove('btn-disabled');
                updateStatus('WebSocket连接中...', false);

                // 更新检测结果区域为等待分析状态
                resultInfoElement.innerHTML = `
                    <div class="empty-state">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 6v6l4 2"></path>
                        </svg>
                        <div class="info-title">等待AI分析</div>
                        <p>视频分析已启动，等待AI完成首次分析...</p>
                    </div>
                `;

                // 重置计数器和性能指标
                frameCount = 0;
                processedCount = 0;
                framesSent = 0;
                framesReceived = 0;
                totalLatency = 0;
                maxLatency = 0;
                minLatency = Infinity;

                // 初始化时间轴和事件数据
                timelineEvents = [];
                allDetectedEvents = [];
                videoStartTime = performance.now();

                // 隐藏加载状态
                loadingOverlay.style.display = 'none';

                // 完成进度条
                updateProgressBar(100);
                setTimeout(() => {
                    updateProgressBar(0);
                }, 500);

                // 显示成功通知
                showNotification('视频处理已成功启动', 'success');

                // 更新连接状态
                updateConnectionHealth('good');

                // 开始处理视频帧
                processVideo();

            } catch (error) {
                console.error('启动失败:', error);
                updateStatus('启动失败: ' + error.message, true);
                loadingOverlay.style.display = 'none';
                updateProgressBar(0);

                // 显示错误通知
                showNotification('启动失败: ' + error.message, 'error');

                // 更新连接状态
                updateConnectionHealth('error');
            }
        }

        // 创建WebSocket连接
        function connectWebSocket() {
            // 关闭之前的连接
            if (ws) {
                ws.close();
            }

            // 创建新连接
            const protocol = location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${location.host}/ws/yolo-video-process`;

            ws = new WebSocket(wsUrl);

            // WebSocket 事件处理
            ws.onopen = function() {
                updateStatus('WebSocket 已连接，开始处理...', false);
            };

            ws.onmessage = function(event) {
                processedCount++;
                const now = performance.now();

                try {
                    const response = JSON.parse(event.data);
                    console.log("收到WebSocket消息:", response);

                    // 添加辅助函数检查是否为Qwen响应
                    function isQwenResponse(response) {
                        return (
                            // 检查是否存在qwen_analysis字段（首选方式）
                            response.qwen_analysis ||
                            // 检查是否是从日志中提取的千问数据
                            (response.log && (
                                response.log.includes("INFO:yolo_video_processor:Qwen API返回成功") ||
                                response.log.includes("INFO:yolo_video_processor:Qwen API原始响应")
                            )) ||
                            // 检查是否包含高风险或低风险事件以及描述（兼容旧格式）
                            (
                                (
                                    (response.high_risk_events && response.high_risk_events.length > 0) ||
                                    (response.low_risk_events && response.low_risk_events.length > 0)
                                ) &&
                                (
                                    response.description &&
                                    (response.description.includes("风险分析") ||
                                     response.description.includes("管控建议"))
                                )
                            )
                        );
                    }

                    // 添加对日志信息的特殊处理
                    if (response.log) {
                        console.log("服务器日志:", response.log);
                        // 检查日志中是否包含Qwen的结果信息
                        if (response.log.includes("Qwen API") || response.log.includes("返回结果结构")) {
                            console.log("检测到Qwen日志信息");
                            // 在状态栏显示日志信息
                            updateStatus("收到Qwen分析: " + response.log.substring(0, 50) + "...", false);
                        }
                    }

                    if (response.error) {
                        console.error('服务器错误:', response.error);
                        updateStatus('服务器错误: ' + response.error, true);
                        updateConnectionHealth('error');
                        return;
                    }

                    // 显示处理后的帧
                    if (response.processed_frame && showDetectionBoxes) {
                        const img = new Image();
                        img.onload = function() {
                            if (!detectionCanvas) return;

                            const ctx = detectionCanvas.getContext('2d');
                            if (ctx) {
                                ctx.clearRect(0, 0, detectionCanvas.width, detectionCanvas.height);
                                ctx.drawImage(img, 0, 0, detectionCanvas.width, detectionCanvas.height);
                            }
                        };
                        img.src = 'data:image/jpeg;base64,' + response.processed_frame;
                    } else if (!showDetectionBoxes && detectionCanvas) {
                        const ctx = detectionCanvas.getContext('2d');
                        if (ctx) {
                            ctx.clearRect(0, 0, detectionCanvas.width, detectionCanvas.height);
                        }
                    }

                    // 处理YOLO检测结果并添加到时间轴
                    if (response.detections && response.detections.length > 0) {
                        const detectedObjects = response.detections.map(det => ({
                            class: det.class || det.label || '未知',
                            confidence: det.confidence || 0,
                            bbox: det.bbox || []
                        }));

                        const description = `检测到 ${detectedObjects.length} 个对象: ${detectedObjects.map(obj => obj.class).join(', ')}`;

                        // 添加到时间轴
                        addTimelineEvent(now, detectedObjects, description);
                    }

                    // 明确检查是否有千问分析结果
                    let hasQwenResult = isQwenResponse(response);

                    // 处理Qwen分析结果
                    if (hasQwenResult) {
                        console.log("检测到Qwen分析结果，准备更新UI");

                        try {
                            let analysisResult = {};

                            // 首选从qwen_analysis中提取数据
                            if (response.qwen_analysis) {
                                const qwenData = response.qwen_analysis;
                                analysisResult = {
                                    summary: {
                                        description: qwenData.description || ''
                                    },
                                    high_risk_events: qwenData.high_risk_events || [],
                                    low_risk_events: qwenData.low_risk_events || []
                                };
                            }
                            // 备选从响应的顶级字段提取数据
                            else {
                                analysisResult = {
                                    summary: {
                                        description: response.description || ''
                                    },
                                    high_risk_events: response.high_risk_events || [],
                                    low_risk_events: response.low_risk_events || []
                                };
                            }

                            // 保存最新的千问分析结果
                            lastQwenResult = analysisResult;
                            lastQwenResultTime = new Date().getTime();

                            // 更新检测结果显示
                            updateResultInfo(analysisResult);
                            showNotification("收到千问AI分析结果", "success");

                        } catch (e) {
                            console.error('处理Qwen分析结果时出错:', e);
                            // 只在无千问分析结果时才显示错误
                            if (!lastQwenResult) {
                                updateResultInfo({
                                    summary: {
                                        description: "处理分析结果时出错: " + e.message
                                    },
                                    high_risk_events: [],
                                    low_risk_events: []
                                });
                            }
                        }
                    }

                    // 检查是否有日志信息包含结果结构 (保留日志提取逻辑以兼容旧版本)
                    if (response.log && !hasQwenResult) {
                        // 尝试从日志中提取有关Qwen分析的信息
                        if (response.log.includes("INFO:yolo_video_processor:返回结果结构:") ||
                            response.log.includes("INFO:yolo_video_processor:Qwen API原始响应")) {
                            try {
                                // 尝试从日志中提取JSON
                                const logText = response.log;

                                // 查找JSON对象使用更精确的模式
                                // 先寻找特定标记后的JSON
                                let jsonText = '';
                                if (logText.includes("INFO:yolo_video_processor:返回结果结构:")) {
                                    jsonText = logText.split("INFO:yolo_video_processor:返回结果结构:")[1].trim();
                                } else if (logText.includes("INFO:yolo_video_processor:Qwen API原始响应")) {
                                    jsonText = logText.split("INFO:yolo_video_processor:Qwen API原始响应")[1].trim();
                                    // 如果包含括号，去掉前面的内容
                                    if (jsonText.includes("(") && jsonText.includes("):")) {
                                        jsonText = jsonText.split("):")[1].trim();
                                    }
                                }

                                // 寻找JSON边界
                                const jsonMatch = jsonText.match(/{[\s\S]*?}/);
                                if (jsonMatch) {
                                    console.log("从日志中提取到JSON:", jsonMatch[0].substring(0, 100) + "...");
                                    // 清理并解析JSON
                                    const cleanJson = jsonMatch[0]
                                        .replace(/'/g, '"')
                                        .replace(/True/g, 'true')
                                        .replace(/False/g, 'false')
                                        .replace(/None/g, 'null');

                                    const parsedJson = JSON.parse(cleanJson);

                                    // 构建并显示结果
                                    const logResult = {
                                        summary: {
                                            description: parsedJson.description || ''
                                        },
                                        high_risk_events: parsedJson.high_risk_events || [],
                                        low_risk_events: parsedJson.low_risk_events || []
                                    };

                                    // 保存最新的千问分析结果
                                    lastQwenResult = logResult;
                                    lastQwenResultTime = new Date().getTime();

                                    // 显示在状态栏
                                    updateStatus("提取到Qwen分析结果", false);

                                    // 更新UI
                                    updateResultInfo(logResult);
                                    showNotification("从日志提取的千问分析结果", "success");
                                    hasQwenResult = true;
                                }
                            } catch (e) {
                                console.error("从日志提取JSON失败:", e);
                            }
                        }
                    }

                    // 检查是否包含taskid，表示后端已启动异步任务
                    if (response.taskid && !hasQwenResult) {
                        // 显示任务正在处理的提示，但不替换已存在的分析结果
                        const emptyState = resultInfoElement.querySelector('.empty-state');
                        if (emptyState || resultInfoElement.innerHTML.trim() === '') {
                            resultInfoElement.innerHTML = `
                                <div class="empty-state">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <path d="M12 6v6l4 2"></path>
                                    </svg>
                                    <div class="info-title">AI分析进行中...</div>
                                    <p>正在处理任务 ID: ${response.taskid}</p>
                                </div>
                            `;
                        }

                        // 定义异步请求任务函数
                        const request_task = async () => {
                            try {
                                console.log(`轮询任务结果，任务ID: ${response.taskid}`);
                                const res = await fetch(`/get_task?taskid=${response.taskid}`);

                                // 无论状态码如何，都尝试处理响应
                                try {
                                    const result = await res.json();
                                    console.log(`任务${response.taskid}轮询结果:`, result);

                                    // 如果结果是500或者包含status: "not_found"，表示任务未完成或未找到
                                    if (result === 500 || (result && result.status === "not_found")) {
                                        // 如果任务未完成或未找到，3秒后重试
                                        console.log(`任务${response.taskid}尚未完成或未找到，3秒后重试`);
                                        setTimeout(async () => {
                                            await request_task();
                                        }, 3000);
                                        return;
                                    }

                                    // 记录已完成的任务ID，防止重复处理
                                    if (!window.completedTasks) {
                                        window.completedTasks = [];
                                    }
                                    if (window.completedTasks.includes(response.taskid)) {
                                        console.log(`任务${response.taskid}已处理，跳过`);
                                        return; // 已经处理过此任务，避免重复更新
                                    }
                                    window.completedTasks.push(response.taskid);

                                    // 确保只处理Qwen API结果
                                    console.log("收到Qwen API结果:", result);

                                    // 检测到千问API返回成功，立即更新UI显示
                                    if (result) {
                                        // 构建分析结果对象
                                        let analysisResult;

                                        if (typeof result === 'string') {
                                            // 尝试解析字符串为JSON
                                            try {
                                                // 处理可能的Python风格引号和布尔值
                                                const cleanedResult = result
                                                    .replace(/'/g, '"')
                                                    .replace(/True/g, 'true')
                                                    .replace(/False/g, 'false')
                                                    .replace(/None/g, 'null');

                                                // 查找JSON对象
                                                const jsonMatch = cleanedResult.match(/\{[\s\S]*\}/);
                                                if (jsonMatch) {
                                                    const parsedJson = JSON.parse(jsonMatch[0]);
                                                    analysisResult = {
                                                        summary: {
                                                            description: parsedJson.description || ''
                                                        },
                                                        high_risk_events: parsedJson.high_risk_events || [],
                                                        low_risk_events: parsedJson.low_risk_events || []
                                                    };
                                                } else {
                                                    // 如果找不到JSON，创建带原始描述的结果
                                                    analysisResult = {
                                                        summary: { description: result },
                                                        high_risk_events: [],
                                                        low_risk_events: []
                                                    };
                                                }
                                            } catch (e) {
                                                console.error("解析任务结果失败:", e);
                                                analysisResult = {
                                                    summary: { description: result },
                                                    high_risk_events: [],
                                                    low_risk_events: []
                                                };
                                            }
                                        } else if (typeof result === 'object' && result !== null) {
                                            // 如果已经是对象
                                            analysisResult = {
                                                summary: {
                                                    description: result.description || ''
                                                },
                                                high_risk_events: result.high_risk_events || [],
                                                low_risk_events: result.low_risk_events || []
                                            };
                                        } else {
                                            // 默认结果
                                            analysisResult = {
                                                summary: { description: "任务完成但结果格式不正确" },
                                                high_risk_events: [],
                                                low_risk_events: []
                                            };
                                        }

                                        // 更新检测结果显示
                                        console.log("更新UI显示Qwen分析结果:", analysisResult);
                                        lastQwenResult = analysisResult; // 保存最新的千问分析结果
                                        lastQwenResultTime = new Date().getTime(); // 更新时间戳
                                        updateResultInfo(analysisResult);

                                        // 显示通知
                                        showNotification("收到AI分析结果", "success");
                                    }
                                } catch (jsonError) {
                                    // JSON解析失败，可能返回的不是JSON格式
                                    console.error('解析任务结果JSON失败:', jsonError);
                                    // 仍然继续尝试，3秒后重试
                                    console.log(`JSON解析失败，3秒后重试任务: ${response.taskid}`);
                                    setTimeout(async () => {
                                        await request_task();
                                    }, 3000);
                                }
                            } catch (error) {
                                // 网络错误或其他请求失败
                                console.error('获取任务结果失败:', error);

                                // 无论发生什么错误，都继续尝试，延长重试间隔到5秒
                                console.log(`请求失败，5秒后重试任务: ${response.taskid}`);
                                setTimeout(async () => {
                                    await request_task();
                                }, 5000);

                                // 只在界面为空时显示错误，否则保留现有内容
                                const emptyState = resultInfoElement.querySelector('.empty-state');
                                if (emptyState && emptyState.querySelector('.info-title').textContent !== 'AI分析进行中...') {
                                    emptyState.querySelector('.info-title').textContent = 'AI分析进行中...';
                                    emptyState.querySelector('p').textContent = `正在重试获取任务 ${response.taskid} 结果...`;
                                }
                            }
                        };

                        // 执行异步任务请求
                        request_task().then(() => console.log("已发起千问API结果请求"));
                    }

                    // 计算延迟
                    if (response.timestamp) {
                        const latency = now - response.timestamp;
                        totalLatency += latency;
                        maxLatency = Math.max(maxLatency, latency);
                        minLatency = Math.min(minLatency, latency);
                        framesReceived++;

                        // 更新当前延迟显示
                        currentLatencyElement.textContent = `${Math.round(latency)} ms`;

                        // 更新实际帧率计算
                        const currentSecond = Math.floor(now / 1000);
                        if (Math.floor(lastSecondStart / 1000) !== currentSecond) {
                            actualFrameRateElement.textContent = `${framesSentLastSecond} FPS`;
                            framesSentLastSecond = 0;
                            lastSecondStart = now;
                        }

                        // 自动调整帧率
                        if (isAutoFrameRate) {
                            adjustFrameRate(latency);
                        }

                        // 根据延迟更新连接健康状态
                        if (latency > 500) {
                            updateConnectionHealth('warning');
                        } else if (latency > 1000) {
                            updateConnectionHealth('error');
                        } else {
                            updateConnectionHealth('good');
                        }

                        // 每秒更新统计信息
                        if (now - lastStatsUpdate > 1000) {
                            updateStats();
                            lastStatsUpdate = now;
                        }
                    }
                } catch (error) {
                    console.error('处理服务器响应时出错:', error);
                    updateConnectionHealth('error');
                }
            };

            ws.onclose = function() {
                if (isRunning) {
                    updateStatus('WebSocket 连接关闭', false);
                    stopProcessing();
                }
            };

            ws.onerror = function(error) {
                console.error('WebSocket 错误:', error);
                updateStatus('WebSocket 错误', true);
                stopProcessing();
            };
        }

        // 捕获并发送视频帧
        async function processVideo() {
            if (!isRunning || !videoElement) return;

            const now = performance.now();
            const elapsed = now - lastFrameTime;

            // 按照设定的帧率处理
            if (elapsed >= framerateInterval) {
                lastFrameTime = now;

                try {
                    // 更新视频当前时间
                    // 如果视频到达末尾，则停止处理
                    if (videoElement.currentTime >= videoElement.duration) {
                        stopProcessing();
                        showNotification('视频处理完成', 'success');

                        // 显示完整总结
                        showCompleteSummary();

                        // 自动切换到总结标签页
                        const summaryTab = document.querySelector('[data-tab="summary"]');
                        if (summaryTab) {
                            summaryTab.click();
                        }

                        return;
                    }

                    // 从视频元素捕获帧
                    if (videoElement.readyState === videoElement.HAVE_ENOUGH_DATA) {
                        // 创建临时画布调整大小和质量
                        const tempCanvas = document.createElement('canvas');
                        const scale = parseFloat(imageSizeSelect.value);
                        tempCanvas.width = videoElement.videoWidth * scale;
                        tempCanvas.height = videoElement.videoHeight * scale;

                        const tempCtx = tempCanvas.getContext('2d');
                        tempCtx.drawImage(videoElement, 0, 0, tempCanvas.width, tempCanvas.height);

                        // 转换为JPEG并调整质量
                        const quality = parseFloat(imageQualitySelect.value);
                        const dataURL = tempCanvas.toDataURL('image/jpeg', quality);
                        const base64Data = dataURL.split(',')[1];

                        // 构造消息并发送
                        if (ws && ws.readyState === WebSocket.OPEN) {
                            const message = {
                                frame: base64Data,
                                timestamp: now,
                                frameId: frameCount,
                                currentTime: videoElement.currentTime,
                                duration: videoElement.duration
                            };

                            ws.send(JSON.stringify(message));
                            framesSent++;
                            frameCount++;
                            framesSentLastSecond++;

                            // 更新视频帧位置
                            videoElement.currentTime += 1.0 / dynamicFrameRate;
                            updateVideoProgress();
                        }
                    }
                } catch (error) {
                    console.error('处理视频帧时出错:', error);
                }
            }

            // 继续处理下一帧
            requestAnimationFrame(processVideo);
        }

        // 停止处理
        function stopProcessing() {
            isRunning = false;

            // 关闭WebSocket连接
            if (ws) {
                ws.close();
                ws = null;
            }

            // 恢复界面状态
            startButton.disabled = false;
            startButton.classList.remove('btn-disabled');
            stopButton.disabled = true;
            stopButton.classList.add('btn-disabled');
            updateStatus('已停止处理', false);

            // 更新最终统计信息
            updateStats(true);
        }

        // 更新检测结果信息
        function updateResultInfo(response) {
            // 只处理包含有效结果数据的响应
            if (!response) return;

            // 添加调试信息
            console.log("正在更新检测结果信息:", response);

            // 提取需要显示的Qwen分析数据
            const description = response.summary && response.summary.description ?
                               response.summary.description :
                               response.description || '';

            // 提取高风险和低风险事件，这是Qwen分析的一部分
            const hasHighRiskEvents = response.high_risk_events && response.high_risk_events.length > 0;
            const hasLowRiskEvents = response.low_risk_events && response.low_risk_events.length > 0;
            const hasSummary = !!description;

            // 记录检测到的内容
            console.log(`检测到的内容: 高风险: ${hasHighRiskEvents ? '有' : '无'}, 低风险: ${hasLowRiskEvents ? '有' : '无'}, 描述: ${hasSummary ? '有' : '无'}`);

            // 避免空结果显示
            const resultElement = document.getElementById('resultInfo');
            if (!resultElement) {
                console.error("无法找到结果显示元素!");
                return;
            }

            // 如果完全没有数据，且有上次分析结果，则保持显示上次结果
            const showEmptyState = !hasHighRiskEvents && !hasLowRiskEvents && !hasSummary;

            if (showEmptyState && lastQwenResult) {
                console.log("保持显示上次的千问分析结果");
                return; // 保留当前显示，不更新
            }

            let infoHtml = '';

            if (showEmptyState && !lastQwenResult) {
                // 显示空状态
                infoHtml = `
                    <div class="empty-state">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="15" y1="9" x2="9" y2="15"></line>
                            <line x1="9" y1="9" x2="15" y2="15"></line>
                        </svg>
                        <div class="info-title">未检测到风险事件</div>
                        <p>当前视频中未检测到任何风险事件或分析结果。</p>
                    </div>
                `;
            } else {
                // 添加时间戳
                const now = new Date();
                const timeString = now.toLocaleTimeString();
                infoHtml += `<div class="result-timestamp">更新时间: ${timeString}`;

                // 如果使用的是缓存的分析结果，显示有效期
                if (lastQwenResultTime > 0 && now.getTime() - lastQwenResultTime > 5000) { // 5秒以上显示有效期
                    infoHtml += ` <span style="color: var(--warning-color);">(${formatTimeDifference(lastQwenResultTime)})</span>`;
                }
                infoHtml += `</div>`;

                // 添加高风险事件信息
                if (hasHighRiskEvents) {
                    infoHtml += '<div class="info-title" style="color: var(--danger-color); margin-top: 16px;">高风险事件:</div>';
                    infoHtml += '<div class="detection-list">';
                    response.high_risk_events.forEach((event, index) => {
                        infoHtml += `
                            <div class="detection-item high-risk-item">
                                <div class="detection-category">${index + 1}. ${event.category || event.label || '未知'}</div>
                                <div class="detection-description">${event.event_description || event.event || '未知事件'}</div>
                            </div>
                        `;
                    });
                    infoHtml += '</div>';
                }

                // 添加低风险事件信息
                if (hasLowRiskEvents) {
                    infoHtml += '<div class="info-title" style="color: var(--warning-color); margin-top: 16px;">低/中风险事件:</div>';
                    infoHtml += '<div class="detection-list">';
                    response.low_risk_events.forEach((event, index) => {
                        infoHtml += `
                            <div class="detection-item low-risk-item">
                                <div class="detection-category">${index + 1}. ${event.category || event.label || '未知'}</div>
                                <div class="detection-description">${event.event_description || event.event || '未知事件'}</div>
                            </div>
                        `;
                    });
                    infoHtml += '</div>';
                }

                // 添加总结信息
                if (description) {
                    infoHtml += '<div class="info-title" style="margin-top: 16px;">场景总结:</div>';
                    infoHtml += `<div class="summary-content">${description}</div>`;
                }
            }

            // 确保结果有内容
            if (infoHtml.trim() === '') {
                infoHtml = `
                    <div class="empty-state">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                            <line x1="9" y1="9" x2="9.01" y2="9"></line>
                            <line x1="15" y1="9" x2="15.01" y2="9"></line>
                        </svg>
                        <div class="info-title">处理中...</div>
                        <p>系统正在分析视频内容，请稍候。</p>
                    </div>
                `;
            }

            // 更新DOM
            resultInfoElement.innerHTML = infoHtml;
            console.log("检测结果已更新到UI");

            // 获取当前活动的标签页
            const activeTab = document.querySelector('.tab-button.active');
            if (activeTab) {
                filterResults(activeTab.dataset.tab);
            }

            // 移除空状态类
            resultInfoElement.classList.remove('empty-state');
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>