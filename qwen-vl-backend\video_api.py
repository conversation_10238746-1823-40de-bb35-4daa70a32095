import json
import os
import re
import cv2
import numpy as np
import tempfile
import time
import base64
import threading
import uuid
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
from openai import OpenAI

# Import functions from detection_api
from detection_api import inference_with_api
from qwen_vl_utils import smart_resize

# Global storage for background processing tasks
video_processing_tasks = {}

def decode_base64_video(base64_string):
    """
    Decode a base64 video string to a temporary file

    Args:
        base64_string (str): Base64 encoded video string

    Returns:
        str: Path to the temporary video file
    """
    # Remove data URL prefix if present
    if "base64," in base64_string:
        base64_string = base64_string.split("base64,")[1]

    # Decode base64 to binary
    video_data = base64.b64decode(base64_string)

    # Create a temporary file
    temp_dir = tempfile.mkdtemp()
    temp_path = os.path.join(temp_dir, "temp_video.mp4")

    # Write the binary data to the temporary file
    with open(temp_path, "wb") as temp_file:
        temp_file.write(video_data)

    return temp_path

def encode_image(image_path):
    """
    Encode an image to base64

    Args:
        image_path (str): Path to the image file

    Returns:
        str: Base64 encoded image
    """
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def process_video_with_qwen_vl_max(video_path, prompt, frame_interval=30, fps=2):
    """
    Process a video using the qwen-vl-max model with MultiModalConversation

    Args:
        video_path (str): Path to the video file
        prompt (str): Prompt for video analysis
        frame_interval (int): Interval of frames to process (deprecated, use fps instead)
        fps (int): Frames per second for video analysis (controls sampling rate)

    Returns:
        dict: Analysis results with streaming response handling
    """
    import os
    import cv2
    import json
    import time
    from dashscope import MultiModalConversation

    try:
        # 检查API密钥
        api_key = os.getenv('DASHSCOPE_API_KEY')
        if not api_key:
            print("DASHSCOPE_API_KEY 环境变量未设置，使用模拟数据")
            return _get_mock_video_result()

        # 检查视频文件是否存在
        if not os.path.exists(video_path):
            print(f"视频文件不存在: {video_path}")
            return {"success": False, "error": f"视频文件不存在: {video_path}"}

        # 获取视频信息
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"无法打开视频文件: {video_path}")
            return {"success": False, "error": f"无法打开视频文件: {video_path}"}

        video_fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / video_fps if video_fps > 0 else 0
        cap.release()

        print(f"视频信息: {frame_count} 帧, {video_fps:.2f} FPS, 时长: {duration:.2f}秒")
        print(f"使用抽帧频率: {fps} FPS (每隔 {1/fps:.2f} 秒抽取一帧)")

        # 构建消息，使用video参数和fps控制
        messages = [
            {
                "role": "user",
                "content": [
                    # fps 参数控制视频抽帧频率，表示每隔1/fps 秒抽取一帧
                    {"video": video_path, "fps": fps},
                    {"text": prompt}
                ]
            }
        ]

        print("开始调用Qwen VL Max API进行视频分析...")
        start_time = time.time()

        # 调用MultiModalConversation API，启用流式处理
        response = MultiModalConversation.call(
            api_key=api_key,
            model="qvq-max",  # 使用qvq-max模型支持视频分析
            messages=messages,
            stream=True,
        )

        # 处理流式响应
        reasoning_content = ""
        answer_content = ""
        is_answering = False

        print("=" * 20 + "思考过程" + "=" * 20)

        for chunk in response:
            # 如果思考过程与回复皆为空，则忽略
            message = chunk.output.choices[0].message
            reasoning_content_chunk = message.get("reasoning_content", None)

            if (chunk.output.choices[0].message.content == [] and
                reasoning_content_chunk == ""):
                continue
            else:
                # 如果当前为思考过程
                if reasoning_content_chunk is not None and chunk.output.choices[0].message.content == []:
                    print(chunk.output.choices[0].message.reasoning_content, end="")
                    reasoning_content += chunk.output.choices[0].message.reasoning_content
                # 如果当前为回复
                elif chunk.output.choices[0].message.content != []:
                    if not is_answering:
                        print("\n" + "=" * 20 + "完整回复" + "=" * 20)
                        is_answering = True
                    print(chunk.output.choices[0].message.content[0]["text"], end="")
                    answer_content += chunk.output.choices[0].message.content[0]["text"]

        processing_time = time.time() - start_time
        print(f"\n\nAPI调用完成，耗时: {processing_time:.2f}秒")

        # 解析分析结果
        analysis_result = _parse_video_analysis_result(answer_content, reasoning_content)

        # 构建返回结果
        result = {
            "success": True,
            "video_metadata": {
                "filename": os.path.basename(video_path),
                "frame_count": frame_count,
                "fps": video_fps,
                "duration": duration,
                "analysis_fps": fps
            },
            "processing_info": {
                "processing_time": processing_time,
                "model": "qvq-max",
                "reasoning_length": len(reasoning_content),
                "answer_length": len(answer_content)
            },
            "reasoning_content": reasoning_content,
            "answer_content": answer_content,
            "analysis": analysis_result,
            "frame_results": _generate_frame_results(analysis_result, duration, fps)
        }

        return result

    except Exception as e:
        print(f"视频分析过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


def _get_mock_video_result():
    """返回模拟的视频分析结果"""
    return {
        "success": True,
        "video_metadata": {
            "filename": "mock_video.mp4",
            "frame_count": 300,
            "fps": 30.0,
            "duration": 10.0,
            "analysis_fps": 2
        },
        "processing_info": {
            "processing_time": 5.0,
            "model": "mock",
            "reasoning_length": 500,
            "answer_length": 200
        },
        "reasoning_content": "这是模拟的思考过程...",
        "answer_content": "这是模拟的分析结果...",
        "analysis": {
            "detections": [
                {
                    "category": "车辆",
                    "event": "speeding_vehicle",
                    "risk_level": "high",
                    "confidence": 0.95,
                    "bbox_2d": [0.1, 0.2, 0.3, 0.6],
                    "label": "超速车辆"
                }
            ],
            "high_risk_events": [
                {
                    "category": "车辆",
                    "event": "speeding_vehicle",
                    "risk_level": "high",
                    "confidence": 0.95,
                    "bbox_2d": [0.1, 0.2, 0.3, 0.6]
                }
            ],
            "low_risk_events": [],
            "description": "该路段存在交通安全风险：1辆车辆超速行驶，存在高风险。"
        },
        "frame_results": [
            {
                "timestamp": 0.0,
                "frame_idx": 0,
                "detections": [
                    {
                        "category": "车辆",
                        "event": "speeding_vehicle",
                        "risk_level": "high",
                        "confidence": 0.95,
                        "bbox_2d": [0.1, 0.2, 0.3, 0.6],
                        "label": "超速车辆"
                    }
                ],
                "high_risk_events": [
                    {
                        "category": "车辆",
                        "event": "speeding_vehicle",
                        "risk_level": "high",
                        "confidence": 0.95,
                        "bbox_2d": [0.1, 0.2, 0.3, 0.6]
                    }
                ],
                "low_risk_events": []
            }
        ]
    }


def _parse_video_analysis_result(answer_content, reasoning_content=None):
    """解析视频分析结果"""
    import json
    import re

    try:
        # 尝试从回答中提取JSON
        json_match = re.search(r'```json\s*(.*?)\s*```', answer_content, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
            return json.loads(json_str)

        # 尝试直接解析整个回答
        if answer_content.strip().startswith('{'):
            return json.loads(answer_content)

        # 如果无法解析JSON，返回基本结构
        return {
            "detections": [],
            "high_risk_events": [],
            "low_risk_events": [],
            "description": answer_content[:500] + "..." if len(answer_content) > 500 else answer_content
        }

    except Exception as e:
        print(f"解析分析结果时出错: {str(e)}")
        return {
            "detections": [],
            "high_risk_events": [],
            "low_risk_events": [],
            "description": f"分析完成，但结果解析失败: {str(e)}"
        }


def _generate_frame_results(analysis_result, duration, fps):
    """根据分析结果生成按时间戳分布的帧结果"""
    frame_results = []

    # 计算分析的帧数
    total_analysis_frames = int(duration * fps)

    if total_analysis_frames <= 0:
        return frame_results

    # 将检测结果分布到不同时间戳
    detections = analysis_result.get("detections", [])
    high_risk_events = analysis_result.get("high_risk_events", [])
    low_risk_events = analysis_result.get("low_risk_events", [])

    for i in range(total_analysis_frames):
        timestamp = i / fps
        frame_idx = int(i * (1 / fps) * 30)  # 假设原视频30fps

        # 简单分布策略：将事件分散到不同时间点
        frame_detections = []
        frame_high_risk = []
        frame_low_risk = []

        if detections and i < len(detections):
            frame_detections = [detections[i % len(detections)]]

        if high_risk_events and i < len(high_risk_events):
            frame_high_risk = [high_risk_events[i % len(high_risk_events)]]

        if low_risk_events and i < len(low_risk_events):
            frame_low_risk = [low_risk_events[i % len(low_risk_events)]]

        frame_results.append({
            "timestamp": timestamp,
            "frame_idx": frame_idx,
            "detections": frame_detections,
            "high_risk_events": frame_high_risk,
            "low_risk_events": frame_low_risk
        })

    return frame_results

def detect_video(video_input, prompt, frame_interval=30, output_folder=None, model_id="qwen-vl-max-1119"):
    """
    Detect objects in a video file or base64 encoded video

    Args:
        video_input (str): Path to the video file or base64 encoded video string
        prompt (str): Prompt for object detection
        frame_interval (int): Process every nth frame
        output_folder (str): Folder to save processed frames
        model_id (str): Model ID to use for inference

    Returns:
        dict: Detection results
    """
    # Check if input is a base64 string
    if isinstance(video_input, str) and (video_input.startswith('data:video') or ';base64,' in video_input):
        video_path = decode_base64_video(video_input)
        temp_video = True
    else:
        video_path = video_input
        temp_video = False

    # Create output folder if not provided
    if output_folder is None:
        output_folder = tempfile.mkdtemp()
        print(f"Created temporary folder for output: {output_folder}")

    os.makedirs(output_folder, exist_ok=True)

    # Process video with qwen-vl-max-1119 model
    if model_id == "qwen-vl-max-1119":
        # 将frame_interval转换为fps，frame_interval=30意味着每30帧取1帧，对于30fps视频相当于1fps
        fps = 30 / frame_interval if frame_interval > 0 else 2
        results = process_video_with_qwen_vl_max(video_path, prompt, frame_interval, fps)
    else:
        # Open the video
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Could not open video file: {video_path}")

        # Get video properties
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        print(f"Video info: {frame_count} frames, {fps} FPS, resolution: {width}x{height}")

        # Load Dashscope API key - 从环境变量获取，而不是硬编码
        # os.environ['DASHSCOPE_API_KEY'] = 'sk-aaf3bf477d4b42eaab64aa9ac835731d'
        min_pixels = 512*28*28
        max_pixels = 2048*28*28

        # Process frames at intervals
        frame_results = []
        detected_frames = []

        try:
            for frame_idx in range(0, frame_count, frame_interval):
                # Set the frame position
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()

                if not ret:
                    print(f"Could not read frame {frame_idx}")
                    continue

                # Calculate timestamp for this frame
                timestamp = frame_idx / fps

                # Convert BGR to RGB (OpenCV uses BGR by default)
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                # Convert to PIL Image
                pil_image = Image.fromarray(rgb_frame)

                # Calculate input dimensions for the API
                input_height, input_width = smart_resize(height, width, min_pixels=min_pixels, max_pixels=max_pixels)

                # Save frame as temporary image
                temp_frame_path = os.path.join(output_folder, f"frame_{frame_idx:06d}.jpg")
                pil_image.save(temp_frame_path)

                # Call API for object detection
                try:
                    print(f"Processing frame {frame_idx}/{frame_count} (timestamp: {timestamp:.2f}s)")
                    response = inference_with_api(
                        pil_image,  # Pass PIL image directly
                        prompt,
                        model_id=model_id,
                        min_pixels=min_pixels,
                        max_pixels=max_pixels
                    )

                    # Parse JSON response
                    try:
                        # Look for JSON within markdown code blocks
                        json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
                        if json_match:
                            json_str = json_match.group(1)
                        else:
                            # Try to parse the entire response as JSON
                            json_str = response

                        # Parse the JSON data
                        detection_data = json.loads(json_str)

                        # Add frame metadata
                        detection_data["frame_idx"] = frame_idx
                        detection_data["timestamp"] = timestamp
                        detection_data["image_path"] = temp_frame_path

                        # Add to results
                        frame_results.append(detection_data)

                        # Draw bounding boxes on the frame
                        annotated_image = pil_image.copy()
                        draw = ImageDraw.Draw(annotated_image)

                        # Draw high risk events
                        if "high_risk_events" in detection_data:
                            for i, event in enumerate(detection_data["high_risk_events"]):
                                if "bbox_2d" in event and len(event["bbox_2d"]) == 4:
                                    color = "red"  # High risk is red
                                    bbox = event["bbox_2d"]
                                    # Convert normalized coordinates to absolute coordinates
                                    abs_x1 = int(bbox[0] * width)
                                    abs_y1 = int(bbox[1] * height)
                                    abs_x2 = int(bbox[2] * width)
                                    abs_y2 = int(bbox[3] * height)

                                    # Draw rectangle
                                    draw.rectangle(
                                        ((abs_x1, abs_y1), (abs_x2, abs_y2)),
                                        outline=color,
                                        width=4
                                    )

                                    # Draw label if available
                                    if "label" in event or "event" in event:
                                        label = event.get("label", event.get("event", "Unknown"))
                                        # Use default font or specify a font
                                        try:
                                            font = ImageFont.truetype("NotoSansCJK-Regular.ttc", size=25)
                                        except IOError:
                                            font = ImageFont.load_default()

                                        draw.text((abs_x1 + 5, abs_y1 - 30), str(label), fill=color, font=font)

                        # Save annotated frame
                        annotated_path = os.path.join(output_folder, f"annotated_{frame_idx:06d}.jpg")
                        annotated_image.save(annotated_path)
                        detected_frames.append({
                            "frame_idx": frame_idx,
                            "timestamp": timestamp,
                            "original_path": temp_frame_path,
                            "annotated_path": annotated_path
                        })

                    except Exception as e:
                        print(f"Error parsing detection results for frame {frame_idx}: {str(e)}")

                except Exception as e:
                    print(f"Error processing frame {frame_idx}: {str(e)}")

        finally:
            # Release the video capture
            cap.release()

            # Clean up temporary file if created
            if temp_video:
                try:
                    os.remove(video_path)
                    os.rmdir(os.path.dirname(video_path))
                except:
                    print(f"Could not remove temporary file: {video_path}")

        # Compile final results
        results = {
            "video_metadata": {
                "filename": os.path.basename(video_path),
                "frame_count": frame_count,
                "fps": fps,
                "width": width,
                "height": height,
                "duration": frame_count / fps
            },
            "processing_info": {
                "frame_interval": frame_interval,
                "frames_processed": len(frame_results),
                "model_id": model_id,
                "output_folder": output_folder
            },
            "frame_results": frame_results,
            "detected_frames": detected_frames
        }

        # Save results to JSON
        results_path = os.path.join(output_folder, "detection_results.json")
        with open(results_path, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"Video processing complete. Results saved to {results_path}")

    return results

def create_video_from_frames(frame_folder, output_path, fps=30, frame_pattern="annotated_*.jpg"):
    """
    Create a video file from a sequence of image frames

    Args:
        frame_folder (str): Folder containing the frames
        output_path (str): Path to save the output video
        fps (int): Frames per second for the output video
        frame_pattern (str): Pattern to match frame files

    Returns:
        str: Path to the output video file
    """
    import glob

    # Find all matching frames
    frame_files = sorted(glob.glob(os.path.join(frame_folder, frame_pattern)))

    if not frame_files:
        raise ValueError(f"No frames found in {frame_folder} matching pattern {frame_pattern}")

    # Get dimensions from the first frame
    first_frame = cv2.imread(frame_files[0])
    height, width, _ = first_frame.shape

    # Create video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    video_writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

    # Write frames to video
    for frame_file in frame_files:
        frame = cv2.imread(frame_file)
        video_writer.write(frame)

    # Release resources
    video_writer.release()

    print(f"Video created and saved to {output_path}")
    return output_path

def process_video_in_background(video_id, video_input, prompt, frame_interval=30, model_id="qwen-vl-max-1119"):
    """
    Process a video in the background and store the results

    Args:
        video_id (str): Unique ID for this video processing task
        video_input (str): Path to video file or base64 video data
        prompt (str): Prompt for object detection
        frame_interval (int): Interval between frames to process
        model_id (str): Model ID to use for detection
    """
    try:
        # Create output folder
        output_folder = os.path.join(tempfile.gettempdir(), f"video_processing_{video_id}")
        os.makedirs(output_folder, exist_ok=True)

        # Update task status
        video_processing_tasks[video_id] = {
            "status": "processing",
            "progress": 0,
            "output_folder": output_folder,
            "start_time": time.time()
        }

        # Process video
        results = detect_video(
            video_input=video_input,
            prompt=prompt,
            frame_interval=frame_interval,
            output_folder=output_folder,
            model_id=model_id
        )

        # Generate output video
        output_video_path = os.path.join(output_folder, "annotated_video.mp4")
        create_video_from_frames(output_folder, output_video_path, fps=results["video_metadata"]["fps"])

        # Update task status
        video_processing_tasks[video_id] = {
            "status": "completed",
            "results": results,
            "output_folder": output_folder,
            "output_video": output_video_path,
            "completion_time": time.time(),
            "start_time": video_processing_tasks[video_id]["start_time"]
        }

    except Exception as e:
        # Update task status with error
        video_processing_tasks[video_id] = {
            "status": "error",
            "error": str(e),
            "output_folder": output_folder if 'output_folder' in locals() else None,
            "completion_time": time.time(),
            "start_time": video_processing_tasks[video_id]["start_time"]
        }

def start_video_processing(video_input, prompt=None, frame_interval=30, fps=2, model_id="qwen-vl-max-1119"):
    """
    Start a background task to process video input

    Args:
        video_input (str): Base64 encoded video string
        prompt (str): Prompt for video analysis
        frame_interval (int): Interval of frames to process (deprecated, use fps instead)
        fps (int): Frames per second for video analysis (controls sampling rate)
        model_id (str): ID of the model to use

    Returns:
        str: Task ID for tracking processing status
    """
    task_id = str(uuid.uuid4())

    # Create a default prompt if none is provided
    if not prompt:
        prompt = f"""
        检测视频中所有帧的安全风险，识别人员、车辆和安全帽，以JSON格式返回结果。
        特别关注未佩戴安全帽的工人（高风险），危险驾驶行为（高风险）和其他安全隐患。

        返回格式示例如下：
        ```json
        {
          "detections": [
            {
              "category": "人员",
              "event": "worker_no_helmet",
              "risk_level": "high",
              "confidence": 0.95,
              "bbox_2d": [0.1, 0.2, 0.3, 0.6],
              "label": "未佩戴安全帽的工人"
            }
          ],
          "high_risk_events": [
            {
              "category": "人员",
              "event": "worker_no_helmet",
              "risk_level": "high",
              "confidence": 0.95,
              "bbox_2d": [0.1, 0.2, 0.3, 0.6]
            }
          ],
          "low_risk_events": [],
          "description": "该建筑工地存在安全隐患：1名工人未佩戴安全帽，存在高风险。"
        }
        ```
        """

    print(f"Starting video processing task {task_id}")

    # Store task status
    video_processing_tasks[task_id] = {
        "status": "processing",
        "start_time": time.time(),
        "prompt": prompt,
        "frame_interval": frame_interval,
        "fps": fps,  # 存储fps参数
        "model_id": model_id,
        "results": None,
        "error": None
    }

    def process_task():
        try:
            # Decode base64 video
            video_path = decode_base64_video(video_input)
            print(f"Video decoded to {video_path}")

            # Process video
            print(f"Processing video with prompt: {prompt[:50]}...")
            print(f"Using fps: {fps} for video analysis")
            results = process_video_with_qwen_vl_max(video_path, prompt, frame_interval, fps)

            if "error" in results:
                video_processing_tasks[task_id]["status"] = "error"
                video_processing_tasks[task_id]["error"] = results["error"]
                print(f"Error processing video for task {task_id}: {results['error']}")
            else:
                video_processing_tasks[task_id]["status"] = "completed"
                video_processing_tasks[task_id]["results"] = results
                print(f"Video processing completed for task {task_id}")

        except Exception as e:
            video_processing_tasks[task_id]["status"] = "error"
            video_processing_tasks[task_id]["error"] = str(e)
            print(f"Exception in video processing task {task_id}: {str(e)}")
            import traceback
            traceback.print_exc()

    # Start processing in a background thread
    thread = threading.Thread(target=process_task)
    thread.daemon = True
    thread.start()

    return task_id

def get_video_processing_status(video_id):
    """
    Get the status of a video processing task

    Args:
        video_id (str): ID of the video processing task

    Returns:
        dict: Status information for the task
    """
    if video_id not in video_processing_tasks:
        return {
            "status": "not_found",
            "error": f"No video processing task found with ID {video_id}"
        }

    task_data = video_processing_tasks[video_id]
    status = task_data["status"]

    response = {
        "video_id": video_id,
        "status": status,
        "start_time": task_data["start_time"],
        "elapsed_time": time.time() - task_data["start_time"]
    }

    if status == "error" and "error" in task_data:
        response["error"] = task_data["error"]

    if status == "completed" and "results" in task_data:
        response["results"] = task_data["results"]

    return response

if __name__ == "__main__":
    # Example usage
    video_path = "path/to/your/video.mp4"
    prompt = """
    检测视频中所有帧的安全风险，识别人员、车辆和安全帽，以JSON格式返回结果。
    特别关注未佩戴安全帽的工人（高风险），危险驾驶行为（高风险）和其他安全隐患。

    返回格式示例如下：
    ```json
    {
      "detections": [
        {
          "category": "人员",
          "event": "worker_no_helmet",
          "risk_level": "high",
          "confidence": 0.95,
          "bbox_2d": [0.1, 0.2, 0.3, 0.6],
          "label": "未佩戴安全帽的工人"
        }
      ],
      "high_risk_events": [
        {
          "category": "人员",
          "event": "worker_no_helmet",
          "risk_level": "high",
          "confidence": 0.95,
          "bbox_2d": [0.1, 0.2, 0.3, 0.6]
        }
      ],
      "low_risk_events": [],
      "description": "该建筑工地存在安全隐患：1名工人未佩戴安全帽，存在高风险。"
    }
    ```
    """

    # Uncomment to run video detection
    # output_folder = "video_detection_results"
    # results = detect_video(video_path, prompt, frame_interval=30, output_folder=output_folder)
    #
    # # Create video with annotated frames
    # output_video = os.path.join(output_folder, "annotated_video.mp4")
    # create_video_from_frames(output_folder, output_video, fps=10)